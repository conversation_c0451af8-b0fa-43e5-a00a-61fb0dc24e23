import * as THREE from "three";
import {Box2, Box3, BufferGeometry, Color, ColorManagement, Euler, LoadingManager, MathUtils, Matrix4, Mesh, Object3D, Plane, Quaternion, Raycaster, Shape, TextureLoader, Vector2, Vector3} from "three";
import {createRandomGenerator} from "@/utility/random";
import {areArraysEqual, calculatePairs, clusterElements, median, removeDuplicates} from "@/utility/arrays";
import {Optional} from "@/model/Optional";
import {TLineSegment3D} from "@/adapter/three/TLineSegment3D";
import {TMeshClusterElement} from "@/adapter/three/TMeshClusterElement";
import {FontLoader} from "three/addons/loaders/FontLoader.js";
import {TMeshCluster} from "@/adapter/three/TMeshCluster";
import {TRendererManager} from "@/adapter/three/TRendererManager";
import {OBJLoader} from "three/addons/loaders/OBJLoader.js";
import {areNumbers<PERSON>qual, isN<PERSON>ber<PERSON><PERSON>r<PERSON><PERSON>, isNumberGreater<PERSON>hanOr<PERSON><PERSON><PERSON>, isNumberLess<PERSON>han, isNumberLessThanOrEqual} from "@/utility/number";
import {TLine2D} from "@/adapter/three/TLine2D";
import {TLineSegment2D} from "@/adapter/three/TLineSegment2D";
import {ADDITION, SUBTRACTION} from "three-bvh-csg";
import {csgCreateBrush_000_GM, csgCreateEvaluator} from "@/adapter/csg/csg-utils";
import {d3IsVector2InsidePolygon} from "@/adapter/d3/d3-utils";
import earcut from "earcut";

export const T_TEXTURE_LOADER = new TextureLoader();
export const T_LOADING_MANAGER = new LoadingManager()
export const T_FONT_LOADER = new FontLoader(T_LOADING_MANAGER)
export const T_OBJ_LOADER = new OBJLoader(T_LOADING_MANAGER)
const T_STAIR_STEP_HEIGHT = 0.195 //in meter (19,5cm)
export let T_POINTER_MOVE_THROTTLE_DELAY = 0 //in ms

export function initializeThree(pointerMoveThrottleDelay: number) {
    //https://threejs.org/docs/index.html#manual/en/introduction/Color-management
    ColorManagement.enabled = true //das ist sehr wichtig in kombination mit three-utility.ts initialize, sonst werden die farben falsch dargestellt

    //https://threejs.org/docs/index.html?q=cache#api/en/loaders/Cache
    THREE.Cache.enabled = true
    THREE.Cache.clear()

    T_POINTER_MOVE_THROTTLE_DELAY = pointerMoveThrottleDelay
}

export function tStepsOfStairOfHeight(stairsHeight: number): number {
    return Math.ceil(stairsHeight / T_STAIR_STEP_HEIGHT)
}

export function tRandomColor(seed: string): Color {
    const randomGenerator = createRandomGenerator(seed)
    return new Color(randomGenerator() * 0xffffff)
}

//TODO: funktion so umbauen, dass alle holes übergeben werden. dann kann die geometrie in einem rutsch erzeugt werden
//TODO: ... außerdem kann man dann in dieser funktion mit hole.clone() arbeiten und muss die holes nicht mehr entfernen sondern kann sie einfach ausblenden
//TODO: ... dann spart man sich auch die renderKeys bei allen meshes, die holes verwenden
/**
 * VORSICHT beim Benutzen, ist nicht besonders sauber alles mit der Matrix-Multiplikation
 */
export function tInsertHole(object: Mesh, hole: Mesh) {
    // hole.applyMatrix4(hole.matrixWorld)

    // noinspection LocalVariableNamingConventionJS
    const objectBrush_000_GM = csgCreateBrush_000_GM(object)
    if (objectBrush_000_GM === null) {
        return
    }
    // noinspection LocalVariableNamingConventionJS
    const holeBrush_000_GM = csgCreateBrush_000_GM(hole)
    if (holeBrush_000_GM === null) {
        tDestroyMesh(objectBrush_000_GM, true, true)
        return
    }

    try {
        // noinspection LocalVariableNamingConventionJS
        const parentWithoutHoleBrush_000_GM = csgCreateEvaluator().evaluate(objectBrush_000_GM, holeBrush_000_GM, SUBTRACTION)

        // noinspection LocalVariableNamingConventionJS
        const newGeometry_000 = parentWithoutHoleBrush_000_GM.geometry.clone()
        newGeometry_000.applyMatrix4(object.matrixWorld.clone().invert())

        object.geometry = newGeometry_000

        tDestroyMesh(parentWithoutHoleBrush_000_GM, true, true)
    } catch (e) {
        console.error("Error while subtracting hole from object", e)
    }

    tDestroyMesh(objectBrush_000_GM, true, true)
    tDestroyMesh(holeBrush_000_GM, true, true)

    hole.removeFromParent()
}

export function tOverrideTransformation(object: Object3D, transformationMatrix: Matrix4) {
    const [translation, rotation, scale] = tDecomposeMatrix(transformationMatrix)

    object.position.copy(translation)
    object.quaternion.copy(rotation)
    object.scale.copy(scale)

    object.updateMatrix()
    object.updateMatrixWorld(true)
}

export function tAreVectors3Propotional(v1: Vector3, v2: Vector3, epsilon: number): boolean {
    const crossProduct = v1.clone().cross(v2)
    return isNumberLessThan(crossProduct.length(), 0, epsilon)
}

/**
 * https://en.wikipedia.org/wiki/Proportionality_(mathematics)
 */
export function tAreVectors2Propotional(v1: Vector2, v2: Vector2, epsilon: number): boolean {
    const crossProduct = v1.clone().cross(v2)
    return isNumberLessThan(crossProduct, 0, epsilon)
}

export function tIsVector2Normalized(vector: Vector2, epsilon: number): boolean {
    return tAreVectors2Equal(vector, vector.clone().normalize(), epsilon)
}

export function tIsVector3Normalized(vector: Vector3, epsilon: number): boolean {
    return tAreVectors3Equal(vector, vector.clone().normalize(), epsilon)
}

export function tLiesDifferenceBetweenVectors3OnDirection(v1: Vector3, v2: Vector3, direction: Vector3, epsilon: number): boolean {
    const difference = v2.clone().sub(v1)
    const crossProduct = difference.cross(direction)
    return isNumberLessThan(crossProduct.length(), 0, epsilon)
}

export function tLiesDifferenceBetweenVectors2OnDirection(v1: Vector2, v2: Vector2, direction: Vector2, epsilon: number): boolean {
    const difference = v2.clone().sub(v1)
    const crossProduct = difference.cross(direction)
    return isNumberLessThan(crossProduct, 0, epsilon)
}

/**
 * https://en.wikipedia.org/wiki/Proportionality_(mathematics)
 */
export function tAreLines2DProportional(line1: TLine2D, line2: TLine2D, epsilon: number): boolean {
    if (!tAreVectors2Parallel(line1.normalizedDirectionXZ, line2.normalizedDirectionXZ, epsilon)) {
        return false
    }
    const difference = line1.positionXZ.clone().sub(line2.positionXZ)
    return tAreVectors2Parallel(line1.normalizedDirectionXZ, difference, epsilon)
}

export function tAreVectors2Parallel(v1: Vector2, v2: Vector2, epsilon: number): boolean {
    return areNumbersEqual(v1.x * v2.y, v1.y * v2.x, epsilon);
}

/**
 * Three.js verwendet keine Epsilon-Checks -.- //TODO bug ticket reporten
 */
export function tAreVectors2Equal(v1: Vector2, v2: Vector2, epsilon: number): boolean {
    return areNumbersEqual(v1.x, v2.x, epsilon)
        && areNumbersEqual(v1.y, v2.y, epsilon)
}

export function tAreVectors3Equal(v1: Vector3, v2: Vector3, epsilon: number): boolean {
    return areNumbersEqual(v1.x, v2.x, epsilon)
        && areNumbersEqual(v1.y, v2.y, epsilon)
        && areNumbersEqual(v1.z, v2.z, epsilon)
}

export function tAreQuaternionsEqual(q1: Quaternion, q2: Quaternion, epsilon: number): boolean {
    return areNumbersEqual(q1.x, q2.x, epsilon)
        && areNumbersEqual(q1.y, q2.y, epsilon)
        && areNumbersEqual(q1.z, q2.z, epsilon)
        && areNumbersEqual(q1.w, q2.w, epsilon)
}

export function tOverrideTransformationWithBillboardEffect(object: Object3D, rendererManager: TRendererManager): void {
    const camera = rendererManager.getCameraManager()?.camera.deref() ?? null
    if (camera === null) {
        return
    }

    const cameraWorldQuaternion = camera.quaternion.clone()

    const parent = object.parent
    if (parent?.type === "Scene") {
        object.quaternion.copy(cameraWorldQuaternion)
        return
    }

    const worldTransformation = parent?.matrixWorld.clone() ?? new Matrix4().identity()
    // noinspection LocalVariableNamingConventionJS
    const [_, worldRotation] = tDecomposeMatrix(worldTransformation)

    const cameraRotationMatrix = new Matrix4().makeRotationFromQuaternion(cameraWorldQuaternion)
    const worldRotationMatrix = new Matrix4().makeRotationFromQuaternion(worldRotation)

    const newRotationMatrix = worldRotationMatrix.clone().invert().multiply(cameraRotationMatrix)

    const [oldTranslation, oldRotation, oldScale] = tDecomposeMatrix(object.matrix)

    const oldTranslationMatrix = new Matrix4().makeTranslation(oldTranslation)
    const oldScaleMatrix = new Matrix4().makeScale(oldScale.x, oldScale.y, oldScale.z)

    //build new transformation without oldRotation
    const newTransformation = oldTranslationMatrix.clone().multiply(newRotationMatrix).multiply(oldScaleMatrix)

    tOverrideTransformation(object, newTransformation)
}

export function tPlaneShape(sizeX: number, sizeY: number): Shape {
    const left = -sizeX / 2
    const right = sizeX / 2
    const top = sizeY / 2
    const bottom = -sizeY / 2

    const planePoints: Vector2[] = [
        new Vector2(left, top),
        new Vector2(right, top),
        new Vector2(right, bottom),
        new Vector2(left, bottom),
    ]
    return new Shape(planePoints)
}

/**
 * Nutzt die x- und z-Koordinaten für die Punkte des Shapes.
 */
export function tTransformShape(shape: Shape, transformationMatrix: Matrix4, reversePoints: boolean): Shape {
    const transformedPoints = shape
        .getPoints()
        .map(point => {
            const transformedVector3 = new Vector3(point.x, 0, point.y)
            transformedVector3.applyMatrix4(transformationMatrix)
            return new Vector2(transformedVector3.x, transformedVector3.z)
        })

    if (reversePoints) {
        transformedPoints.reverse()
    }

    return new Shape(transformedPoints)
}

/**
 * Berechnet den Winkel, um den die Linien gedreht werden müssen, damit sie parallel zur x-Achse liegen.
 *
 * @param lineSegments Linien in der Form [[startPoint, endPoint], …]. Die Y-Koordinate wird ignoriert.
 *
 * @return Winkel im Bogenmaß/Radiant (radian) entlang der Y-Achse
 */
export function tDeskewAngle3D(lineSegments: readonly TLineSegment3D[]): Optional<number> {
    const lineSegmentAngles: number[] = [];

    for (const lineSegment of lineSegments) {
        const startPoint = lineSegment.start;
        const endPoint = lineSegment.end;

        const lineVector = endPoint.clone().sub(startPoint);

        let angle = Math.atan2(lineVector.z, lineVector.x);
        if (angle < 0) {
            angle += Math.PI;
        }
        if (angle > Math.PI / 2) {
            angle -= Math.PI / 2;
        }

        lineSegmentAngles.push(angle);

        // console.log(MathUtils.radToDeg(angle))
    }

    return median(lineSegmentAngles); //wichtig ist hier der median, sonst können einzelne ausreißer das gesamtergebnis verfälschen
}

/**
 * Berechnet den Winkel, um den die Linien gedreht werden müssen, damit sie parallel zur x-Achse liegen.
 *
 * @param lineSegments Linien in der Form [[startPoint, endPoint], …]. Die Y-Koordinate wird ignoriert.
 *
 * @return Winkel im Bogenmaß/Radiant (radian) entlang der Y-Achse
 */
export function tDeskewAngle2D(lineSegments: readonly TLineSegment2D[]): Optional<number> {
    const lineSegmentAngles: number[] = [];

    for (const lineSegment of lineSegments) {
        const startPoint = lineSegment.start;
        const endPoint = lineSegment.end;

        const lineVector = endPoint.clone().sub(startPoint);

        let angle = Math.atan2(lineVector.y, lineVector.x);
        if (angle < 0) {
            angle += Math.PI;
        }
        if (angle > Math.PI / 2) {
            angle -= Math.PI / 2;
        }

        lineSegmentAngles.push(angle);

        // console.log(MathUtils.radToDeg(angle))
    }

    return median(lineSegmentAngles); //wichtig ist hier der median, sonst können einzelne ausreißer das gesamtergebnis verfälschen
}

/**
 * @return null, wenn beide Meshes übereinander liegen
 */
export function tLineSegmentBetween(mesh1: Mesh, mesh2: Mesh): Optional<TLineSegment3D> {
    mesh1.updateMatrixWorld()
    mesh2.updateMatrixWorld()

    const position1 = new Vector3()
    const position2 = new Vector3()

    position1.setFromMatrixPosition(mesh1.matrixWorld)
    position2.setFromMatrixPosition(mesh2.matrixWorld)

    const raycaster1 = new Raycaster()
    const raycaster2 = new Raycaster()

    raycaster1.set(position1, position2.clone().sub(position1).normalize())
    raycaster2.set(position2, position1.clone().sub(position2).normalize())

    const intersections1 = raycaster1.intersectObject(mesh2)
    const intersections2 = raycaster2.intersectObject(mesh1)

    if (intersections1.length > 0 && intersections2.length > 0) {
        return {
            start: intersections1[0].point,
            end: intersections2[0].point
        }
    }

    return null
}

export function tLineSegmentsBetween(meshes: Mesh[]): TLineSegment3D[] {
    if (meshes.length <= 0) {
        return []
    }

    return calculatePairs(meshes)
        .map(meshPair => tLineSegmentBetween(meshPair[0], meshPair[1]))
        .filter(line => line !== null)
}

export function tAreMeshesNearby(mesh1: Mesh, mesh2: Mesh, maxDistance: number, epsilon: number): boolean {
    const line = tLineSegmentBetween(mesh1, mesh2)
    if (line === null) {
        return true //intersection!
    }
    const distance = line.start.distanceTo(line.end)
    return isNumberLessThanOrEqual(distance, maxDistance, epsilon)
}

export function tIsMeshNearbyAnyMesh(mesh: Mesh, meshes: Mesh[], maxDistance: number, epsilon: number): boolean {
    return meshes.some(otherMesh => tAreMeshesNearby(mesh, otherMesh, maxDistance, epsilon))
}

export function tClusterMeshes<T, E extends TMeshClusterElement<T>, C extends TMeshCluster<T>>(
    meshClusterElements: readonly E[],
    joinFunction: (cluster: C, element: E) => boolean,
    mergeFunction?: (cluster: C, element: E) => void,
    clusterCreator?: (cluster: TMeshCluster<T>, element: E) => C
): C[] {
    if (meshClusterElements.length <= 0) {
        return []
    }
    return clusterElements<C, E>(
        meshClusterElements,
        joinFunction,
        (cluster, element) => {
            cluster.elements.push(element)

            const oldMesh = cluster.mesh

            // noinspection LocalVariableNamingConventionJS
            const clusterBrush_000_GM = csgCreateBrush_000_GM(cluster.mesh)
            if (clusterBrush_000_GM === null) {
                return
            }
            // noinspection LocalVariableNamingConventionJS
            const elementBrush_000_GM = csgCreateBrush_000_GM(element.mesh)
            if (elementBrush_000_GM === null) {
                tDestroyMesh(clusterBrush_000_GM, true, true)
                return
            }

            try {
                // noinspection LocalVariableNamingConventionJS
                const mergedBrush_000_GM = csgCreateEvaluator().evaluate(clusterBrush_000_GM, elementBrush_000_GM, ADDITION)

                // noinspection LocalVariableNamingConventionJS
                const geometry_000 = mergedBrush_000_GM.geometry.clone()
                geometry_000.applyMatrix4(oldMesh.matrixWorld.clone().invert())

                cluster.mesh = new Mesh(geometry_000)

                tDestroyMesh(mergedBrush_000_GM, true, true)
                tDestroyMesh(oldMesh, true, true)
            } catch (e) {
                console.error("Error while merging meshes", e)
            }

            tDestroyMesh(clusterBrush_000_GM, true, true)
            tDestroyMesh(elementBrush_000_GM, true, true)

            mergeFunction?.(cluster, element)
        },
        element => {
            const cluster: TMeshCluster<T> = {
                elements: [element],
                mesh: element.mesh.clone(true),
            }
            return clusterCreator === undefined
                ? cluster as unknown as C
                : clusterCreator(cluster, element)
        }
    )
}

export function tDestroyMesh(mesh: Mesh, destroyGeometry: boolean, destroyMaterials: boolean) {
    mesh.removeFromParent()

    if (destroyGeometry) {
        mesh.geometry.dispose()
    }

    if (destroyMaterials) {
        if (mesh.material instanceof Array) {
            for (const material of mesh.material) {
                material.dispose()
            }
        } else {
            mesh.material.dispose()
        }
    }
}

/**
 * @return [translation, rotation, scale]
 */
export function tDecomposeMatrix(matrix: Matrix4): [Vector3, Quaternion, Vector3] {
    const translation = new Vector3()
    const rotation = new Quaternion()
    const scale = new Vector3()
    matrix.decompose(translation, rotation, scale)
    return [translation, rotation, scale]
}

/**
 * @return [translation, rotation, scale]
 */
export function tDecomposeMatrixToComponents(matrix: Matrix4): [Matrix4, Matrix4, Matrix4] {
    const [translation, rotation, scale] = tDecomposeMatrix(matrix)
    return [
        new Matrix4().makeTranslation(translation),
        new Matrix4().makeRotationFromQuaternion(rotation),
        new Matrix4().makeScale(scale.x, scale.y, scale.z)
    ]
}

export function tVectorsToTranslationMatrices(vectors: Vector3[]): Matrix4[] {
    return vectors.map(vector => new Matrix4().makeTranslation(
        vector.x,
        vector.y,
        vector.z
    ))
}

export function tAreMatricesEqual(matrix1: Matrix4, matrix2: Matrix4, epsilon: number): boolean {
    return areArraysEqual(matrix1.elements, matrix2.elements, (a, b) => areNumbersEqual(a, b, epsilon))
}

export function tObjectToVertices3D(object: Object3D & { geometry: BufferGeometry }): Vector3[] {
    const vertices: Vector3[] = []
    const positions = object.geometry.getAttribute("position")

    for (let i = 0; i < positions.count; ++i) {
        vertices.push(new Vector3(
            positions.getX(i),
            positions.getY(i),
            positions.getZ(i)
        ))
    }

    object.updateMatrix()
    object.updateMatrixWorld(true)

    return vertices.map(v => object.localToWorld(v.clone()))
}

/**
 * @param object Object
 * @param deduplicationPrecision Anzahl der Nachkommastellen, die für die Deduplizierung verwendet werden. Einheit: Meter. 100 bedeutet 2 Nachkommastellen => 0.01m => 1cm.
 */
export function tObjectToVertices2D(object: Object3D & { geometry: BufferGeometry }, deduplicationPrecision: number = 100): Vector2[] {
    const vertices = tObjectToVertices3D(object).map(vertex => new Vector2(vertex.x, vertex.z))
    return removeDuplicates(vertices, v => {
        const roundedX = Math.round(v.x * deduplicationPrecision) / deduplicationPrecision
        const roundedY = Math.round(v.y * deduplicationPrecision) / deduplicationPrecision

        return `${roundedX}|${roundedY}`
    })
}

export function areTrianglePoints2DCollinear(p1: Vector2, p2: Vector2, p3: Vector2, epsilon: number): boolean {
    const v1 = p2.clone().sub(p1)
    const v2 = p3.clone().sub(p2)

    return tAreVectors2Parallel(v1, v2, epsilon)
}

export function tReduceCollinearPoints2D(points: readonly Vector2[], epsilon: number): Vector2[] {
    if (points.length < 4) { //3 Punkte + erster und letzter Punkt sind gleich
        return []
    }

    const reducedPoints: Vector2[] = [points[0]]

    for (let i = 1; i < points.length - 1; ++i) {
        const p1 = reducedPoints[reducedPoints.length - 1]
        const p2 = points[i]
        const p3 = points[i + 1]

        if (!areTrianglePoints2DCollinear(p1, p2, p3, epsilon)) {
            reducedPoints.push(p2)
        }
    }

    reducedPoints.push(points[points.length - 1])

    return reducedPoints
}

export function tAverageVector3Of(vectorArrays: readonly (readonly number[])[]): Vector3 {
    return vectorArrays
        .map(v => new Vector3().fromArray(v))
        .reduce((a, b) => a.add(b), new Vector3())
        .divideScalar(vectorArrays.length)
}

export function tAverageVector2Of(vectorArrays: readonly (readonly number[])[]): Vector2 {
    return vectorArrays
        .map(v => new Vector2().fromArray(v))
        .reduce((a, b) => a.add(b), new Vector2())
        .divideScalar(vectorArrays.length)
}

export function tAreLineSegmentsOverlapping(lineSegment1: TLineSegment2D, lineSegment2: TLineSegment2D, epsilon: number): boolean {
    const dir1 = new Vector2().subVectors(lineSegment1.end, lineSegment1.start);
    const dir2 = new Vector2().subVectors(lineSegment2.end, lineSegment2.start);

    // Prüfe, ob die Vektoren parallel sind (dir1.cross(dir2) === 0 bedeutet parallel in 2D)
    if (areNumbersEqual(dir1.cross(dir2), 0, epsilon)) {
        // console.log("nicht parallel", Math.abs(dir1.cross(dir2)))
        // Die Segmente sind nicht parallel
        return false;
    }
    if (tIsPointOnLineSegment(lineSegment1, lineSegment2.start, epsilon)) {
        return true;
    }
    if (tIsPointOnLineSegment(lineSegment1, lineSegment2.end, epsilon)) {
        return true;
    }
    if (tIsPointOnLineSegment(lineSegment2, lineSegment1.start, epsilon)) {
        return true;
    }
    // noinspection RedundantIfStatementJS
    if (tIsPointOnLineSegment(lineSegment2, lineSegment1.end, epsilon)) {
        return true
    }
    return false;
}

export function tIsPointOnLineSegment(lineSegment: TLineSegment2D, point: Vector2, epsilon: number): boolean {
    const {start, end} = lineSegment;

    const startToEnd = new Vector2().subVectors(end, start);
    const startToPoint = new Vector2().subVectors(point, start);

    // Prüfe, ob der Punkt auf der Linie liegt (ob die Vektoren parallel sind)
    if (areNumbersEqual(startToEnd.cross(startToPoint), 0, epsilon)) {
        return false;
    }

    // Prüfe, ob der Punkt innerhalb des Segmentes liegt (zwischen Start und Ende)
    const dotProduct = startToPoint.dot(startToEnd);
    return !(isNumberLessThan(dotProduct, 0, epsilon) || isNumberGreaterThan(dotProduct, startToEnd.lengthSq(), epsilon))

    // Check if the point lies within the bounding box of the segment
    // const withinXBounds = (point.x >= Math.min(start.x, end.x) - epsilon) && (point.x <= Math.max(start.x, end.x) + epsilon);
    // const withinYBounds = (point.y >= Math.min(start.y, end.y) - epsilon) && (point.y <= Math.max(start.y, end.y) + epsilon);
    //
    // // The point must be collinear and within the bounds of the segment
    // return withinXBounds && withinYBounds;
}

export function tLineSegment2DIntersectionPoint(lineSegment1: TLineSegment2D, lineSegment2: TLineSegment2D, epsilon: number): Optional<Vector2> {
    const p1 = lineSegment1.start
    const p2 = lineSegment1.end
    const p3 = lineSegment2.start
    const p4 = lineSegment2.end

    const d1 = p2.clone().sub(p1)
    const d2 = p4.clone().sub(p3)

    const d = d1.x * d2.y - d1.y * d2.x
    if (areNumbersEqual(d, 0, epsilon)) {
        return null // Lines are parallel
    }

    // noinspection OverlyComplexArithmeticExpressionJS
    const t1 = ((p3.x - p1.x) * d2.y - (p3.y - p1.y) * d2.x) / d
    // noinspection OverlyComplexArithmeticExpressionJS
    const t2 = ((p3.x - p1.x) * d1.y - (p3.y - p1.y) * d1.x) / d

    if (
        isNumberLessThan(t1, 0, epsilon) ||
        isNumberGreaterThan(t1, 1, epsilon) ||
        isNumberLessThan(t2, 0, epsilon) ||
        isNumberGreaterThan(t2, 1, epsilon)
    ) {
        return null // Intersection is outside the line segments
    }

    return new Vector2(
        p1.x + t1 * d1.x,
        p1.y + t1 * d1.y
    )
}

export function tLineIntersectionPoint(line1: TLine2D, line2: TLine2D, epsilon: number): Optional<Vector2> {
    const p1 = line1.positionXZ
    const d1 = line1.normalizedDirectionXZ
    const p2 = line2.positionXZ
    const d2 = line2.normalizedDirectionXZ

    const d = d1.x * d2.y - d1.y * d2.x
    if (areNumbersEqual(d, 0, epsilon)) {
        return null
    }

    // noinspection OverlyComplexArithmeticExpressionJS
    const t1 = ((p2.x - p1.x) * d2.y - (p2.y - p1.y) * d2.x) / d
    //t2 is not needed

    const intersectionX = p1.x + t1 * d1.x
    const intersectionY = p1.y + t1 * d1.y

    return new Vector2(
        intersectionX,
        intersectionY
    )

    // const dx = p2.x - p1.x

    // const det = d1.x * d2.y - d1.y * d2.x;
    //
    // if (det === 0) {
    //     // Lines are parallel
    //     return null;
    // }
    //
    // const t = ((p2.x - p1.x) * d2.y - (p2.y - p1.y) * d2.x) / det;
    // return new Vector2(p1.x + t * d1.x, p1.y + t * d1.y);

    // const denominator = snapLine1.normalizedDirectionXZ.y * snapLine2.normalizedDirectionXZ.x - snapLine1.normalizedDirectionXZ.x * snapLine2.normalizedDirectionXZ.y
    // if (denominator === 0) {
    //     return null //parallel
    // }
    //
    // // noinspection OverlyComplexArithmeticExpressionJS
    // const t = (snapLine1.normalizedDirectionXZ.x * (snapLine2.positionXZ.y - snapLine1.positionXZ.y) - snapLine1.normalizedDirectionXZ.y * (snapLine2.positionXZ.x - snapLine1.positionXZ.x)) / denominator
    // // noinspection OverlyComplexArithmeticExpressionJS
    // const u = (snapLine2.normalizedDirectionXZ.x * (snapLine2.positionXZ.y - snapLine1.positionXZ.y) - snapLine2.normalizedDirectionXZ.y * (snapLine2.positionXZ.x - snapLine1.positionXZ.x)) / denominator
    //
    // if (t < 0 || t > 1 || u < 0 || u > 1) {
    //     return null
    // }
    //
    // return snapLine1.positionXZ.clone().add(snapLine1.normalizedDirectionXZ.clone().multiplyScalar(t))
}

export function tVectors3ToBox3(vectors: readonly Vector3[]): Box3 {
    const box = new Box3()
    for (const vector of vectors) {
        box.expandByPoint(vector)
    }
    return box
}

export function tVectors2ToBox2(vectors: readonly Vector2[]): Box2 {
    const box = new Box2()
    for (const vector of vectors) {
        box.expandByPoint(vector)
    }
    return box
}

export function tSizeOfVectors3(vectors: readonly Vector3[]): Vector3 {
    if (vectors.length <= 0) {
        return new Vector3(0, 0, 0)
    }
    const bbox = tVectors3ToBox3(vectors)
    return bbox.getSize(new Vector3())
}

export function tSizeOfVectors2(vectors: readonly Vector2[]): Vector2 {
    const bbox = tVectors2ToBox2(vectors)
    return bbox.getSize(new Vector2())
}

export function tCenterOfVectors3(vectors: readonly Vector3[]): Vector3 {
    const bbox = tVectors3ToBox3(vectors)
    return bbox.getCenter(new Vector3())
}

export function tCenterOfVectors2(vectors: readonly Vector2[]): Vector2 {
    const bbox = tVectors2ToBox2(vectors)
    return bbox.getCenter(new Vector2())
}

export function tVectors2OfTheBiggestDistance(vectors: readonly Vector2[], epsilon: number): Optional<[Vector2, Vector2]> {
    if (vectors.length < 2) {
        return null
    }

    let maxDistancePoints: [Vector2, Vector2] = [vectors[0], vectors[1]]
    if (vectors.length === 2) {
        return maxDistancePoints
    }

    let maxDistance = 0
    for (let i = 0; i < vectors.length; ++i) {
        for (let j = i + 1; j < vectors.length; ++j) {
            const distance = vectors[i].distanceTo(vectors[j])
            if (isNumberGreaterThan(distance, maxDistance, epsilon)) {
                maxDistance = distance
                maxDistancePoints = [vectors[i], vectors[j]]
            }
        }
    }

    return maxDistancePoints
}

/**
 * Wandelt beliebige Punkte in ein Polygon um.
 * Die Reihenfolge der Punkte kann beliebig sein.
 * Hinterher entsteht ein Polygon mit einer festen Punktreihenfolge, sodass keine Überlappungen entstehen.
 * //TODO: ungetestet
 */
export function tPointsToPolygon(points: readonly Vector2[]): Vector2[] {
    if (points.length <= 4) { //Erster und letzter Punkt sind gleich und es müssen mindestens 3 Punkte sein
        return [...points]
    }

    const centroid = tCenterOfVectors2(points)

    return points
        .map(point => {
            const angle = Math.atan2(point.y - centroid.y, point.x - centroid.x)
            return {point, angle}
        })
        .sort((a, b) => a.angle - b.angle)
        .map(a => a.point)
}

export function tRotateVector2Clockwise90Degrees(vector: Vector2): Vector2 {
    return new Vector2(vector.y, -vector.x)
}

export function tRotateVector2CounterClockwise90Degrees(vector: Vector2): Vector2 {
    return new Vector2(-vector.y, vector.x)
}

export function tOrthogonalNormalOnVector2(vector: Vector2): Vector2 {
    return tRotateVector2CounterClockwise90Degrees(vector).normalize()
}

export function tClosestPointXZOnLine2D(line: TLine2D, pointXZ: Vector2): Vector2 {
    const lineToPointXZ = pointXZ.clone().sub(line.positionXZ)
    const projectionLength = lineToPointXZ.dot(line.normalizedDirectionXZ)
    return line.normalizedDirectionXZ.clone().multiplyScalar(projectionLength).add(line.positionXZ)
}

export function tClosestPointOnLineSegment2D(lineSegment: TLineSegment2D, point: Vector2, epsilon: number): Vector2 {
    const start = lineSegment.start
    const end = lineSegment.end

    const lineToPoint = point.clone().sub(start)
    const lineToEnd = end.clone().sub(start)
    const projectionLength = lineToPoint.dot(lineToEnd) / lineToEnd.lengthSq()

    if (isNumberLessThanOrEqual(projectionLength, 0, epsilon)) {
        return start
    }
    if (isNumberGreaterThanOrEqual(projectionLength, 1, epsilon)) {
        return end
    }
    return start.clone().add(lineToEnd.multiplyScalar(projectionLength))
}

export function tVector2ToEdgeDistance(point: Vector2, polygon: readonly Vector2[], epsilon: number): number {
    let minDistance = Number.POSITIVE_INFINITY;

    if (polygon.length < 4) { //mindestens 3 Punkte + erster Punkt um Polygon zu schließen
        return minDistance;
    }

    const vertices = polygon.slice(0, -1); //letzter Punkt ist der erste Punkt

    for (let i = 0; i < vertices.length; ++i) {
        const v1 = vertices[i];
        const v2 = vertices[(i + 1) % vertices.length];

        if (tAreVectors2Equal(v1, v2, epsilon)) {
            continue;
        }

        const lineSegment: TLineSegment2D = {
            start: v1,
            end: v2
        }

        const closestPoint = tClosestPointOnLineSegment2D(lineSegment, point, epsilon)
        const distance = closestPoint.distanceTo(point);

        minDistance = Math.min(minDistance, distance);
    }

    return minDistance;
}

// noinspection FunctionTooLongJS
/**
 * @param polygon
 * @param holes
 * @param epsilon
 * @param minDistanceFromHoles Mindestabstand zu Löchern. Default: 0.5 (50cm)
 * @param samplesPerMeter Anzahl der Samples pro Meter. Default: 10 (10cm)
 * @param maxSamples Maximale Anzahl der Samples. Default: 50_000. Wird das Limit überschritten, wird null zurückgegeben.
 */
export function tVectors2ToCentralPoint(
    polygon: readonly Vector2[],
    holes: readonly (readonly Vector2[])[],
    epsilon: number,
    minDistanceFromHoles: number = 0.5,
    samplesPerMeter: number = 10,
    maxSamples: number = 50_000
): Optional<Vector2> {
    if (polygon.length < 4) { //mindestens 3 Punkte + erster Punkt um Polygon zu schließen
        return null
    }
    const filteredHoles = holes.filter(hole => hole.length >= 4) //mindestens 3 Punkte + erster Punkt um Polygon zu schließen

    const box = tVectors2ToBox2(polygon);
    const center = box.getCenter(new Vector2());
    const size = box.getSize(new Vector2());

    const widthStepSize = 1 / samplesPerMeter;
    const heightStepSize = 1 / samplesPerMeter;

    const widthSteps = Math.max(Math.ceil(size.x / widthStepSize), 1);
    const heightSteps = Math.max(Math.ceil(size.y / heightStepSize), 1);

    if (widthSteps * heightSteps > maxSamples) {
        console.warn("tVectors2ToCentralPoint: Too many samples", widthSteps, "x", heightSteps, "=", widthSteps * heightSteps)
        return null;
    }

    let bestPoint: Optional<Vector2> = center;
    let maxMinDistance = Number.NEGATIVE_INFINITY;

    // console.log("size", size)
    // console.log("widthSteps", widthSteps)
    // console.log("heightSteps", heightSteps)
    //console.log("steps", widthSteps * heightSteps)
    // console.log("x", box.min.x, box.max.x)
    // console.log("y", box.min.y, box.max.y)
    //
    // if (widthSteps < 2) {
    //     console.log(">>> BREITE")
    // }
    // if (heightSteps < 2) {
    //     console.log(">>> HÖHE")
    // }

    for (let i = 1; i < widthSteps; ++i) { //min und max koordinate wird durch "= 1" und "< widthSteps" verhindert, was gut ist
        for (let j = 1; j < heightSteps; ++j) { //min und max koordinate wird durch "= 1" und "< heightSteps" verhindert, was gut ist
            const x = box.min.x + (i / widthSteps) * size.x;
            const y = box.min.y + (j / heightSteps) * size.y;

            // console.log(x, y)

            const point = new Vector2(x, y);

            if (d3IsVector2InsidePolygon(point, polygon) && filteredHoles.every(hole => !d3IsVector2InsidePolygon(point, hole))) {
                const minDistanceToHoles = filteredHoles.length <= 0 ? 0 : Math.min(...filteredHoles.map(hole => tVector2ToEdgeDistance(point, hole, epsilon)));
                if (filteredHoles.length > 0 && isNumberLessThan(minDistanceToHoles, minDistanceFromHoles, epsilon)) {
                    continue
                }

                const minDistance = tVector2ToEdgeDistance(point, polygon, epsilon)

                if (isNumberGreaterThan(minDistance, maxMinDistance, epsilon)) {
                    maxMinDistance = minDistance;
                    bestPoint = point;
                }
            }
        }
    }
    return bestPoint;
}

export function tShapeToLinesSegments(shape: Shape, divisions: number): Vector2[] {
    const points = shape.getPoints(divisions).slice(0, -1) //letzter Punkt ist der erste Punkt
    const lines: Vector2[] = []
    for (let i = 0; i < points.length; ++i) {
        const p1 = points[i]
        const p2 = points[(i + 1) % points.length]
        lines.push(p1)
        lines.push(p2)
    }
    return lines
}

/**
 * @return [center, radius]
 */
export function tCalculateCircleCenterAndRadius(start: Vector2, startAngleInDegree: number, end: Vector2, endAngleInDegree: number): [Vector2, number] {
    const thetaInDegree = Math.abs(endAngleInDegree - startAngleInDegree)
    // const startAngle = MathUtils.degToRad(startAngleInDegree)
    // const endAngle = MathUtils.degToRad(endAngleInDegree)
    const theta = MathUtils.degToRad(thetaInDegree)

    const d = start.distanceTo(end)
    const radius = d / (2 * Math.sin(theta / 2))

    const midPoint = new Vector2().addVectors(start, end).multiplyScalar(0.5);
    const perpendicular = new Vector2(end.y - start.y, start.x - end.x).normalize();
    const distanceToCenter = Math.sqrt(radius * radius - (d / 2) * (d / 2));

    const circleCenter1 = midPoint.clone().add(perpendicular.clone().multiplyScalar(distanceToCenter));
    const circleCenter2 = midPoint.clone().add(perpendicular.clone().multiplyScalar(-distanceToCenter));

    // const startToCenter1 = circleCenter1.clone().sub(start).normalize();
    // const startToCenter2 = circleCenter2.clone().sub(start).normalize();

    // const angle1 = Math.atan2(startToCenter1.y, startToCenter1.x);
    // const angle2 = Math.atan2(startToCenter2.y, startToCenter2.x);

    const circleCenter = thetaInDegree > 180 ? circleCenter2 : circleCenter1// Math.abs(angle1 - startAngle) < Math.abs(angle2 - startAngle) ? circleCenter1 : circleCenter2;

    return [circleCenter, radius];
}

export function tExtractParametersFromProjectionMatrixOfPerspectiveCamera(projectMatrix: Matrix4) {
    const m = projectMatrix.elements
    const fov = 2 * Math.atan(1 / m[5]) * (180 / Math.PI)
    const aspect = m[5] / m[0]
    const near = m[14] / (m[10] - 1)
    const far = m[14] / (m[10] + 1)

    return {fov, aspect, near, far}
}

export function tRoundRotationOfTransformationTo90DegreeSteps(transformation: Matrix4): Matrix4 {
    const [translation, rotation, scale] = tDecomposeMatrix(transformation)

    const euler = new Euler().setFromQuaternion(rotation)

    let rotationY = MathUtils.radToDeg(euler.y)

    rotationY = Math.round(rotationY / 90) * 90

    //hier müsste man eig. mit modulo arbeiten...
    if (rotationY < 0) {
        rotationY += 360
    } else if (rotationY >= 360) {
        rotationY -= 360
    }

    euler.y = MathUtils.degToRad(rotationY)
    rotation.setFromEuler(euler)

    return new Matrix4().compose(translation, rotation, scale)
}

export function tRotationYInDegreeFromTransformation(transformation: Matrix4): number {
    const [translation, rotation] = tDecomposeMatrix(transformation)
    const euler = new Euler().setFromQuaternion(rotation)
    return MathUtils.radToDeg(euler.y)
}

export function tAngleInRadiansBetweenLineSegments2D(line1: TLineSegment2D, line2: TLineSegment2D): number {
    const v1 = line1.end.clone().sub(line1.start).normalize()
    const v2 = line2.end.clone().sub(line2.start).normalize()
    return v1.angleTo(v2)
}

export function tAngleInRadiansBetweenLineSegments3D(line1: TLineSegment3D, line2: TLineSegment3D): number {
    const v1 = line1.end.clone().sub(line1.start).normalize()
    const v2 = line2.end.clone().sub(line2.start).normalize()
    return v1.angleTo(v2)
}

/**
 * Triangulates a concave 3D polygon using projection and earcut.
 * @param points3D - An array of Vector3 points defining the polygon.
 * @returns An array of triangles (each represented as a set of 3 indices).
 */
export function tTriangulateConcave3DPolygon(points3D: readonly Vector3[]): [number[], Vector2[]] {
    if (points3D.length < 3) {
        return [[], []]
    }

    // Find the best-fitting plane
    const plane = new Plane();
    const normal = tCalculateBestFittingPlaneNormal(points3D);
    plane.setFromNormalAndCoplanarPoint(normal, points3D[0]);

    // Project points onto the 2D plane
    const projectedPoints = tProjectPointsToPlane(points3D, plane);
    const flatPoints = projectedPoints.flatMap(p => [p.x, p.y]);

    return [earcut(flatPoints), projectedPoints]
}

/**
 * Computes a best-fitting normal for a set of points.
 */
export function tCalculateBestFittingPlaneNormal(points: readonly Vector3[]): Vector3 {
    const centroid = new Vector3();
    points.forEach(p => centroid.add(p));
    centroid.divideScalar(points.length);

    const normal = new Vector3();
    for (let i = 0; i < points.length; i++) {
        const p1 = points[i].clone().sub(centroid);
        const p2 = points[(i + 1) % points.length].clone().sub(centroid);
        normal.add(p1.cross(p2));
    }
    return normal.normalize();
}

//TODO: epsilon
function calculateOrthogonalVector(normal: Vector3): Vector3 {
    // Pick a vector that is not parallel to the normal
    const tangent = new Vector3(1, 0, 0);
    if (Math.abs(normal.x) > 0.99) { // If normal is too close to (1,0,0), use a different vector
        tangent.set(0, 1, 0);
    }
    // Compute orthogonal vector
    return new Vector3().crossVectors(normal, tangent).normalize();
}

/**
 * Projects 3D points onto a best-fitting plane to obtain 2D coordinates.
 */
function tProjectPointsToPlane(points: readonly Vector3[], plane: Plane): Vector2[] {
    const basisX = new Vector3(), basisY = new Vector3();
    basisX.copy(calculateOrthogonalVector(plane.normal));
    basisY.crossVectors(plane.normal, basisX);

    return points.map(p => {
        const relative = p.clone().sub(points[0]);
        return new Vector2(relative.dot(basisX), relative.dot(basisY));
    });
}

// noinspection FunctionNamingConventionJS
export function tPolygon3DToBufferGeometry_000(polygon: readonly Vector3[]): BufferGeometry {
    if (polygon.length <= 0) {
        return new BufferGeometry()
    }

    //POSITION
    const geometry = new BufferGeometry().setFromPoints(polygon.map(p => p.clone()))

    //INDEX
    const [indices, projectedPoints] = tTriangulateConcave3DPolygon(polygon)
    geometry.setIndex(indices)

    //UV
    const minX = Math.min(...projectedPoints.map(p => p.x));
    const maxX = Math.max(...projectedPoints.map(p => p.x));
    const minY = Math.min(...projectedPoints.map(p => p.y));
    const maxY = Math.max(...projectedPoints.map(p => p.y));

    const uvs = projectedPoints.map(p => new THREE.Vector2(
        (p.x - minX) / (maxX - minX),
        (p.y - minY) / (maxY - minY),
    ));
    const uvArray = new Float32Array(uvs.flatMap(uv => [uv.x, uv.y]));
    geometry.setAttribute("uv", new THREE.BufferAttribute(uvArray, 2));

    //NORMAL
    geometry.computeVertexNormals()

    return geometry
}

export function tIsPolygonClockwise3D(vertices: readonly Vector3[]): boolean {
    if (vertices.length < 3) {
        throw new Error("At least 3 points are required");
    }

    // Step 1: Compute normal using Newell’s method
    const normal = new Vector3(0, 0, 0);
    const n = vertices.length;

    for (let i = 0; i < n; i++) {
        const current = vertices[i];
        const next = vertices[(i + 1) % n]; // Wrap around

        normal.x += (current.y - next.y) * (current.z + next.z);
        normal.y += (current.z - next.z) * (current.x + next.x);
        normal.z += (current.x - next.x) * (current.y + next.y);
    }

    normal.normalize(); // Get unit normal

    // Step 2: Find dominant axis for 2D projection
    const absNormal = new Vector3(
        Math.abs(normal.x),
        Math.abs(normal.y),
        Math.abs(normal.z)
    );

    let projectedPoints: Vector2[];

    if (absNormal.x >= absNormal.y && absNormal.x >= absNormal.z) {
        // Project onto YZ plane (drop X)
        projectedPoints = vertices.map(v => new Vector2(v.y, v.z));
    } else if (absNormal.y >= absNormal.x && absNormal.y >= absNormal.z) {
        // Project onto XZ plane (drop Y)
        projectedPoints = vertices.map(v => new Vector2(v.x, v.z));
    } else {
        // Project onto XY plane (drop Z)
        projectedPoints = vertices.map(v => new Vector2(v.x, v.y));
    }

    // Step 3: Compute signed area using correct shoelace formula
    let sum = 0;
    for (let i = 0; i < projectedPoints.length; i++) {
        const current = projectedPoints[i];
        const next = projectedPoints[(i + 1) % projectedPoints.length];
        sum += current.x * next.y - next.x * current.y;
    }

    // Step 4: Determine orientation
    // Positive sum = counter-clockwise, negative sum = clockwise
    return sum < 0; // Clockwise if sum is negative
}

export function tIsPolygon3DOnPlane(polygon: readonly Vector3[], plane: Plane, epsilon: number): boolean {
    return polygon.every(p => areNumbersEqual(plane.distanceToPoint(p), 0, epsilon))
}

export function tSurfaceAreaOfBufferGeometry(bufferGeometry: BufferGeometry): number {
    const positions = bufferGeometry.attributes.position.array;
    const indices = bufferGeometry.getIndex()!.array

    let surfaceArea = 0;

    for (let i = 0; i < indices.length; i += 3) {
        const a = indices[i] * 3;
        const b = indices[i + 1] * 3;
        const c = indices[i + 2] * 3;

        const vA = new Vector3(positions[a], positions[a + 1], positions[a + 2]);
        const vB = new Vector3(positions[b], positions[b + 1], positions[b + 2]);
        const vC = new Vector3(positions[c], positions[c + 1], positions[c + 2]);

        surfaceArea += tTriangleArea(vA, vB, vC);
    }

    return surfaceArea;
}

export function tTriangleArea(a: Vector3, b: Vector3, c: Vector3): number {
    const ab = b.clone().sub(a);
    const ac = c.clone().sub(a);

    return 0.5 * ab.cross(ac).length();
}

export function tEdgeLengthsOfPolygon3D(polygon: readonly Vector3[], epsilon: number): number[] {
    if (polygon.length < 3) {
        throw new Error("At least 3 points are required");
    }
    const isClosed = tAreVectors3Equal(polygon[0], polygon[polygon.length - 1], epsilon);

    const edgeLengths: number[] = []

    for (let i = 0; i < polygon.length - (isClosed ? 1 : 0); ++i) {
        const a = polygon[i]
        const b = polygon[(i + 1) % polygon.length]
        edgeLengths.push(a.distanceTo(b))
    }

    return edgeLengths
}

export function tIsPolygon3DRectangular(polygon: readonly Vector3[], epsilon: number): [boolean, number, number] {
    if (polygon.length !== 4) {
        return [false, 0, 0]
    }
    const [a, b, c, d] = polygon

    const ab = a.distanceTo(b)
    const bc = b.distanceTo(c)
    const cd = c.distanceTo(d)
    const da = d.distanceTo(a)

    const diagonal1 = a.distanceTo(c)
    const diagonal2 = b.distanceTo(d)

    const isRectangle = areNumbersEqual(ab, cd, epsilon) && areNumbersEqual(bc, da, epsilon) && areNumbersEqual(diagonal1, diagonal2, epsilon)
    return [isRectangle, ab, bc]
}

export function tPerimeterOfPolygon3D(polygon: readonly Vector3[], epsilon: number, edgeLengths: readonly number[] = tEdgeLengthsOfPolygon3D(polygon, epsilon)): number {
    return edgeLengths.reduce((a, b) => a + b, 0)
}